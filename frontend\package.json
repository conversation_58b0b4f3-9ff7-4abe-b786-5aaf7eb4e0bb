{"name": "veslint-frontend", "version": "2.0.0", "private": true, "description": "VESLINT - AI-Powered Maritime Intelligence Platform", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@mui/x-data-grid": "^6.20.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "firebase": "^10.12.2", "framer-motion": "^10.18.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "recharts": "^2.12.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^18.19.113", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0"}, "keywords": ["maritime", "vessel-classification", "ais-data", "machine-learning", "react", "typescript", "firebase"], "author": "VESLINT Team", "license": "MIT"}