/* Global styles for Crystal Intelligence theme */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 
               'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 
               'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #0A192F;
  color: #CCD6F6;
  line-height: 1.6;
}

code {
  font-family: 'Fira Code', 'Monaco', 'Consolas', 'Ubuntu Mono', monospace;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(17, 34, 64, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(100, 255, 218, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 255, 218, 0.5);
}

/* Selection styling */
::selection {
  background: rgba(100, 255, 218, 0.3);
  color: #CCD6F6;
}

::-moz-selection {
  background: rgba(100, 255, 218, 0.3);
  color: #CCD6F6;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #64FFDA;
  outline-offset: 2px;
}

/* Link styles */
a {
  color: #64FFDA;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #7FFFD4;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(100, 255, 218, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.6);
  }
}

/* Utility classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  body {
    background: #000000;
    color: #FFFFFF;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}