{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyPO6RdRj4/8h0OCoVOc80mK"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "PyPox6r4EbIW", "executionInfo": {"status": "ok", "timestamp": 1750980983630, "user_tz": -60, "elapsed": 7256763, "user": {"displayName": "<PERSON>", "userId": "11563385763805944126"}}, "outputId": "491f12bd-2b89-4217-c905-ba2e98052e56"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📦 Installing advanced ML libraries...\n", "🚀 MARITIME VESSEL CLASSIFICATION - EXTREME PERFORMANCE\n", "======================================================================\n", "🎯 Target: >95% Accuracy | 🏆 World-Class Implementation\n", "🧠 Advanced Feature Engineering + Hyperparameter Optimization\n", "======================================================================\n", "🚀 Starting EXTREME PERFORMANCE maritime classification...\n", "🔍 Checking system resources...\n", "📊 System Memory: 12.7GB Total, 10.5GB Available\n", "💾 Current Usage: 1432.6MB\n", "Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n", "☁️  Google Drive mounted successfully\n", "📂 Loading AIS data...\n", "📄 Extracting: AIS_2024_10_24.csv\n", "⚠️  Limited to 550000 records for memory efficiency\n", "✅ Loaded 550,000 records\n", "🧹 Advanced data cleaning...\n", "✅ Advanced cleaning: 550,000 → 93,980 records (17.1% retained)\n", "🧠 Extracting world-class features...\n", "✅ Extracted 99 world-class features for 1795 vessels\n", "\n", "📈 EXTREME Dataset Summary:\n", "   Features: 99 (world-class feature engineering)\n", "   Samples: 1795 (high-quality filtered)\n", "   Classes: 4\n", "   Class Distribution:\n", "     TUG: 1103 (61.4%)\n", "     FISHING: 329 (18.3%)\n", "     PLEASURE: 252 (14.0%)\n", "     CARGO: 111 (6.2%)\n", "🧠 Training EXTREME PERFORMANCE ensemble...\n", "🔍 Performing feature selection...\n", "📊 Selected 50 most informative features\n", "⚖️  Applying advanced class balancing...\n", "📈 Training data: 3728 samples after balancing\n", "🌲 Training optimized Random Forest...\n", "🌳 Training Extra Trees...\n", "⚡ Training extreme LightGBM...\n", "🚀 Training extreme XGBoost...\n", "🐱 Training CatBoost...\n", "📈 Training Gradient Boosting...\n", "🎯 Generating meta-features...\n", "🧠 Training meta-learner...\n", "✅ EXTREME training completed in 7221.3s\n", "🏆 FINAL META-ENSEMBLE ACCURACY: 0.8815 (88.15%)\n", "   rf: 0.8296\n", "   et: 0.8259\n", "   lgb: 0.8333\n", "   xgb: 0.8333\n", "   cat: 0.8333\n", "   gb: 0.8519\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 2400x1800 with 10 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "================================================================================\n", "🏆 EXTREME PERFORMANCE PIPELINE COMPLETED!\n", "🎯 FINAL META-ENSEMBLE ACCURACY: 0.8815 (88.15%)\n", "📈 Good performance, consider data quality improvements\n", "================================================================================\n", "\n", "🏆 EXTREME PERFORMANCE MODEL READY!\n", "💾 Save: joblib.dump(model, 'extreme_maritime_classifier.joblib')\n"]}], "source": ["# Maritime Vessel Classification - EXTREME PERFORMANCE IMPLEMENTATION\n", "# Target: >95% Accuracy with Advanced ML Techniques\n", "# World-Class Professional Implementation\n", "\n", "import os\n", "import sys\n", "import time\n", "import warnings\n", "import gc\n", "import zipfile\n", "from pathlib import Path\n", "from typing import Dict, List, Tuple, Any, Optional\n", "import psutil\n", "\n", "# Core data science libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, GradientBoostingClassifier\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, f1_score\n", "from sklearn.utils.class_weight import compute_class_weight\n", "from sklearn.feature_selection import SelectKBest, f_classif, RFE\n", "from sklearn.linear_model import LogisticRegression\n", "from lightgbm import LGBMClassifier\n", "from imblearn.over_sampling import SMOTE, ADASYN\n", "from imblearn.combine import SMOTETomek\n", "import joblib\n", "\n", "# Advanced ML libraries\n", "try:\n", "    from xgboost import XGBClassifier\n", "    from catboost import CatBoostClassifier\n", "    ADVANCED_MODELS_AVAILABLE = True\n", "except ImportError:\n", "    print(\"📦 Installing advanced ML libraries...\")\n", "    import subprocess\n", "    subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", \"xgboost\", \"catboost\", \"-q\"])\n", "    from xgboost import XGBClassifier\n", "    from catboost import CatBoostClassifier\n", "    ADVANCED_MODELS_AVAILABLE = True\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'\n", "\n", "print(\"🚀 MARITIME VESSEL CLASSIFICATION - EXTREME PERFORMANCE\")\n", "print(\"=\" * 70)\n", "print(\"🎯 Target: >95% Accuracy | 🏆 World-Class Implementation\")\n", "print(\"🧠 Advanced Feature Engineering + Hyperparameter Optimization\")\n", "print(\"=\" * 70)\n", "\n", "class ResourceManager:\n", "    \"\"\"Advanced resource management\"\"\"\n", "\n", "    @staticmethod\n", "    def get_memory_usage():\n", "        process = psutil.Process(os.getpid())\n", "        return process.memory_info().rss / 1024 / 1024  # MB\n", "\n", "    @staticmethod\n", "    def optimize_memory():\n", "        gc.collect()\n", "        return ResourceManager.get_memory_usage()\n", "\n", "    @staticmethod\n", "    def check_resources():\n", "        memory = psutil.virtual_memory()\n", "        print(f\"📊 System Memory: {memory.total / (1024**3):.1f}GB Total, {memory.available / (1024**3):.1f}GB Available\")\n", "        print(f\"💾 Current Usage: {ResourceManager.get_memory_usage():.1f}MB\")\n", "        return memory.available / (1024**3) > 1\n", "\n", "class AdvancedDataProcessor:\n", "    \"\"\"Advanced AIS data processing with sophisticated feature engineering\"\"\"\n", "\n", "    def __init__(self, config: Dict[str, Any]):\n", "        self.config = config\n", "        self.vessel_type_mapping = {\n", "            30: 'FISHING',\n", "            31: 'TUG',\n", "            37: 'PLEASURE',\n", "            52: 'TUG',\n", "            70: 'CARGO'\n", "        }\n", "\n", "    def load_and_validate_data(self, zip_path: str) -> pd.DataFrame:\n", "        \"\"\"Load and validate AIS data\"\"\"\n", "        print(\"📂 Loading AIS data...\")\n", "\n", "        with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "            file_list = zip_ref.namelist()\n", "            csv_files = [f for f in file_list if f.endswith('.csv')]\n", "\n", "            if not csv_files:\n", "                raise ValueError(\"No CSV files found in ZIP archive\")\n", "\n", "            csv_file = max(csv_files, key=lambda x: zip_ref.getinfo(x).file_size)\n", "            print(f\"📄 Extracting: {csv_file}\")\n", "\n", "            with zip_ref.open(csv_file) as f:\n", "                chunk_size = 50000\n", "                chunks = []\n", "\n", "                for chunk in pd.read_csv(f, chunksize=chunk_size, low_memory=False):\n", "                    chunks.append(chunk)\n", "                    if len(chunks) * chunk_size > 500000:\n", "                        print(f\"⚠️  Limited to {len(chunks) * chunk_size} records for memory efficiency\")\n", "                        break\n", "\n", "                df = pd.concat(chunks, ignore_index=True)\n", "\n", "        print(f\"✅ Loaded {len(df):,} records\")\n", "        ResourceManager.optimize_memory()\n", "\n", "        return self._validate_required_columns(df)\n", "\n", "    def _validate_required_columns(self, df: pd.DataFrame) -> pd.DataFrame:\n", "        \"\"\"Validate and ensure required columns exist\"\"\"\n", "        required_cols = ['LAT', 'LON', 'SOG', 'COG', 'VesselType', 'MMSI', 'BaseDateTime']\n", "\n", "        col_mapping = {}\n", "        for col in required_cols:\n", "            if col in df.columns:\n", "                continue\n", "\n", "            variations = {\n", "                'LAT': ['Latitude', 'lat', 'latitude'],\n", "                'LON': ['Longitude', 'lon', 'longitude'],\n", "                'SOG': ['Speed', 'sog', 'speed'],\n", "                'COG': ['Course', 'cog', 'course'],\n", "                'VesselType': ['VesselAndCargoType', 'vessel_type'],\n", "                'MMSI': ['mmsi'],\n", "                'BaseDateTime': ['Timestamp', 'DateTime', 'timestamp']\n", "            }\n", "\n", "            for var in variations.get(col, []):\n", "                if var in df.columns:\n", "                    col_mapping[var] = col\n", "                    break\n", "            else:\n", "                raise ValueError(f\"Required column '{col}' not found\")\n", "\n", "        df = df.rename(columns=col_mapping)\n", "\n", "        keep_cols = required_cols.copy()\n", "        for col in ['Length', 'Width', 'Draft', 'length', 'width', 'draft']:\n", "            if col in df.columns:\n", "                keep_cols.append(col)\n", "\n", "        return df[keep_cols].copy()\n", "\n", "    def advanced_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:\n", "        \"\"\"Advanced data cleaning with statistical outlier detection\"\"\"\n", "        print(\"🧹 Advanced data cleaning...\")\n", "        initial_rows = len(df)\n", "\n", "        # Convert timestamp\n", "        df['BaseDateTime'] = pd.to_datetime(df['BaseDateTime'], errors='coerce')\n", "        df = df.dropna(subset=['BaseDateTime'])\n", "\n", "        # Remove invalid coordinates\n", "        df = df[\n", "            (df['LAT'].between(-90, 90)) &\n", "            (df['LON'].between(-180, 180)) &\n", "            (df['LAT'] != 0) & (df['LON'] != 0)\n", "        ]\n", "\n", "        # Advanced speed filtering using IQR method\n", "        Q1_speed = df['SOG'].quantile(0.25)\n", "        Q3_speed = df['SOG'].quantile(0.75)\n", "        IQR_speed = Q3_speed - Q1_speed\n", "        speed_lower = max(0, Q1_speed - 1.5 * IQR_speed)\n", "        speed_upper = min(60, Q3_speed + 1.5 * IQR_speed)  # Cap at 60 knots\n", "        df = df[df['SOG'].between(speed_lower, speed_upper)]\n", "\n", "        # Valid course (0-359)\n", "        df = df[df['COG'].between(0, 359)]\n", "\n", "        # Focus on main vessel types\n", "        df = df[df['VesselType'].isin(self.vessel_type_mapping.keys())]\n", "\n", "        # Advanced trajectory filtering - require minimum points AND time span\n", "        vessel_stats = df.groupby('MMSI').agg({\n", "            'BaseDateTime': ['count', lambda x: (x.max() - x.min()).total_seconds() / 3600],\n", "            'SOG': 'std',\n", "            'COG': 'std'\n", "        }).round(2)\n", "        vessel_stats.columns = ['point_count', 'time_span_hours', 'speed_std', 'course_std']\n", "\n", "        # Filter vessels with sufficient data quality\n", "        valid_vessels = vessel_stats[\n", "            (vessel_stats['point_count'] >= self.config['min_trajectory_length']) &\n", "            (vessel_stats['time_span_hours'] >= 0.5) &  # At least 30 minutes\n", "            (vessel_stats['speed_std'] > 0.1) &  # Some speed variation\n", "            (vessel_stats['course_std'] > 1)     # Some course variation\n", "        ].index\n", "\n", "        df = df[df['MMSI'].isin(valid_vessels)]\n", "\n", "        print(f\"✅ Advanced cleaning: {initial_rows:,} → {len(df):,} records ({100*len(df)/initial_rows:.1f}% retained)\")\n", "\n", "        df['VesselTypeClean'] = df['VesselType'].map(self.vessel_type_mapping)\n", "        return df\n", "\n", "    def extract_world_class_features(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:\n", "        \"\"\"Extract world-class features for >95% accuracy\"\"\"\n", "        print(\"🧠 Extracting world-class features...\")\n", "\n", "        features_list = []\n", "\n", "        for mmsi in df['MMSI'].unique():\n", "            vessel_data = df[df['MMSI'] == mmsi].sort_values('BaseDateTime')\n", "\n", "            if len(vessel_data) < self.config['min_trajectory_length']:\n", "                continue\n", "\n", "            vessel_type = vessel_data['VesselTypeClean'].iloc[0]\n", "\n", "            # Convert to numpy arrays\n", "            speeds = vessel_data['SOG'].values\n", "            courses = vessel_data['COG'].values\n", "            lats = vessel_data['LAT'].values\n", "            lons = vessel_data['LON'].values\n", "            timestamps = vessel_data['BaseDateTime'].values\n", "\n", "            # Time analysis\n", "            time_diffs = np.diff([pd.Timestamp(t).timestamp() for t in timestamps]) / 3600  # hours\n", "            time_span = (timestamps[-1] - timestamps[0]) / np.timedelta64(1, 'h')\n", "\n", "            # === ADVANCED SPEED FEATURES ===\n", "            speed_features = self._extract_advanced_speed_features(speeds)\n", "\n", "            # === ADVANCED COURSE FEATURES ===\n", "            course_features = self._extract_advanced_course_features(courses)\n", "\n", "            # === ADVANCED GEOGRAPHIC FEATURES ===\n", "            geo_features = self._extract_advanced_geographic_features(lats, lons)\n", "\n", "            # === ADVANCED TEMPORAL FEATURES ===\n", "            temporal_features = self._extract_advanced_temporal_features(time_diffs, time_span, len(vessel_data))\n", "\n", "            # === VESSEL BEHAVIOR SIGNATURES ===\n", "            behavior_features = self._extract_behavior_signatures(speeds, courses, lats, lons, time_diffs)\n", "\n", "            # === OPERATIONAL PATTERN FEATURES ===\n", "            operational_features = self._extract_operational_patterns(speeds, courses, time_diffs)\n", "\n", "            # === STATISTICAL COMPLEXITY FEATURES ===\n", "            complexity_features = self._extract_complexity_features(speeds, courses, lats, lons)\n", "\n", "            # === PHYSICAL CHARACTERISTICS ===\n", "            physical_features = self._extract_physical_features(vessel_data)\n", "\n", "            # Combine all features\n", "            vessel_features = {\n", "                'MMSI': mmsi,\n", "                'VesselType': vessel_type,\n", "                **speed_features,\n", "                **course_features,\n", "                **geo_features,\n", "                **temporal_features,\n", "                **behavior_features,\n", "                **operational_features,\n", "                **complexity_features,\n", "                **physical_features\n", "            }\n", "\n", "            features_list.append(vessel_features)\n", "\n", "        features_df = pd.DataFrame(features_list)\n", "        features_df = features_df.fillna(0)\n", "\n", "        print(f\"✅ Extracted {len(features_df.columns)-2} world-class features for {len(features_df)} vessels\")\n", "\n", "        X = features_df.drop(['MMSI', 'VesselType'], axis=1)\n", "        y = features_df['VesselType']\n", "\n", "        return X, y\n", "\n", "    def _extract_advanced_speed_features(self, speeds):\n", "        \"\"\"Extract sophisticated speed features\"\"\"\n", "        if len(speeds) == 0:\n", "            return {}\n", "\n", "        # Basic statistics\n", "        speed_stats = {\n", "            'speed_mean': np.mean(speeds),\n", "            'speed_median': np.median(speeds),\n", "            'speed_std': np.std(speeds),\n", "            'speed_var': np.var(speeds),\n", "            'speed_min': np.min(speeds),\n", "            'speed_max': np.max(speeds),\n", "            'speed_range': np.max(speeds) - np.min(speeds),\n", "            'speed_iqr': np.percentile(speeds, 75) - np.percentile(speeds, 25),\n", "        }\n", "\n", "        # Advanced statistical measures\n", "        speed_advanced = {\n", "            'speed_skewness': self._safe_skewness(speeds),\n", "            'speed_kurtosis': self._safe_kurtosis(speeds),\n", "            'speed_cv': np.std(speeds) / max(np.mean(speeds), 0.1),\n", "            'speed_gini': self._gini_coefficient(speeds),\n", "        }\n", "\n", "        # Percentiles and quantiles\n", "        percentiles = [5, 10, 25, 75, 90, 95]\n", "        speed_percentiles = {f'speed_p{p}': np.percentile(speeds, p) for p in percentiles}\n", "\n", "        # Speed pattern analysis\n", "        speed_patterns = {\n", "            'stopped_ratio': np.mean(speeds < 0.5),\n", "            'very_slow_ratio': np.mean(speeds < 2),\n", "            'slow_ratio': np.mean((speeds >= 2) & (speeds < 5)),\n", "            'medium_ratio': np.mean((speeds >= 5) & (speeds < 12)),\n", "            'fast_ratio': np.mean((speeds >= 12) & (speeds < 20)),\n", "            'very_fast_ratio': np.mean(speeds >= 20),\n", "            'cruise_speed_ratio': np.mean((speeds >= 8) & (speeds <= 15)),\n", "        }\n", "\n", "        # Speed change analysis\n", "        if len(speeds) > 1:\n", "            speed_changes = np.abs(np.diff(speeds))\n", "            speed_change_features = {\n", "                'speed_change_mean': np.mean(speed_changes),\n", "                'speed_change_std': np.std(speed_changes),\n", "                'speed_change_max': np.max(speed_changes),\n", "                'acceleration_events': np.mean(speed_changes > 2),\n", "                'speed_stability': 1 - (np.std(speed_changes) / max(np.mean(speeds), 0.1)),\n", "            }\n", "        else:\n", "            speed_change_features = {\n", "                'speed_change_mean': 0, 'speed_change_std': 0, 'speed_change_max': 0,\n", "                'acceleration_events': 0, 'speed_stability': 1\n", "            }\n", "\n", "        return {**speed_stats, **speed_advanced, **speed_percentiles, **speed_patterns, **speed_change_features}\n", "\n", "    def _extract_advanced_course_features(self, courses):\n", "        \"\"\"Extract sophisticated course features\"\"\"\n", "        if len(courses) <= 1:\n", "            return {f'course_{k}': 0 for k in ['change_mean', 'change_std', 'change_max', 'stability',\n", "                                              'sharp_turns', 'direction_changes', 'zigzag_pattern',\n", "                                              'circular_variance', 'turning_radius_est']}\n", "\n", "        # Calculate course changes handling 360-degree wraparound\n", "        course_changes = []\n", "        for i in range(1, len(courses)):\n", "            diff = courses[i] - courses[i-1]\n", "            if diff > 180:\n", "                diff -= 360\n", "            elif diff < -180:\n", "                diff += 360\n", "            course_changes.append(abs(diff))\n", "\n", "        course_changes = np.array(course_changes)\n", "\n", "        # Basic course change statistics\n", "        course_stats = {\n", "            'course_change_mean': np.mean(course_changes),\n", "            'course_change_std': np.std(course_changes),\n", "            'course_change_max': np.max(course_changes),\n", "            'course_change_median': np.median(course_changes),\n", "        }\n", "\n", "        # Course stability and patterns\n", "        course_patterns = {\n", "            'course_stability': 1 - (np.std(course_changes) / 180),\n", "            'small_turns_ratio': np.mean(course_changes < 5),\n", "            'medium_turns_ratio': np.mean((course_changes >= 5) & (course_changes < 30)),\n", "            'sharp_turns_ratio': np.mean(course_changes >= 30),\n", "            'very_sharp_turns_ratio': np.mean(course_changes >= 90),\n", "            'direction_changes': np.sum(course_changes > 45),\n", "        }\n", "\n", "        # Advanced course analysis\n", "        course_advanced = {\n", "            'zigzag_pattern': self._detect_zigzag_pattern(courses),\n", "            'circular_variance': self._circular_variance(courses),\n", "            'turning_radius_est': self._estimate_turning_radius(course_changes),\n", "            'course_entropy': self._calculate_entropy(course_changes, bins=36),  # 10-degree bins\n", "        }\n", "\n", "        return {**course_stats, **course_patterns, **course_advanced}\n", "\n", "    def _extract_advanced_geographic_features(self, lats, lons):\n", "        \"\"\"Extract sophisticated geographic features\"\"\"\n", "        if len(lats) == 0:\n", "            return {}\n", "\n", "        # Basic geographic bounds\n", "        geo_basic = {\n", "            'lat_min': np.min(lats),\n", "            'lat_max': np.max(lats),\n", "            'lon_min': np.min(lons),\n", "            'lon_max': np.max(lons),\n", "            'lat_range': np.max(lats) - np.min(lats),\n", "            'lon_range': np.max(lons) - np.min(lons),\n", "            'lat_center': np.mean(lats),\n", "            'lon_center': np.mean(lons),\n", "            'lat_std': np.std(lats),\n", "            'lon_std': np.std(lons),\n", "        }\n", "\n", "        # Area and shape analysis\n", "        geo_area = {\n", "            'bounding_box_area': (np.max(lats) - np.min(lats)) * (np.max(lons) - np.min(lons)),\n", "            'convex_hull_ratio': self._convex_hull_ratio(lats, lons),\n", "            'area_efficiency': len(set(zip(np.round(lats, 3), np.round(lons, 3)))) / len(lats),\n", "        }\n", "\n", "        # Distance and trajectory analysis\n", "        if len(lats) > 1:\n", "            distances = self._calculate_distances(lats, lons)\n", "            geo_trajectory = {\n", "                'total_distance': np.sum(distances),\n", "                'avg_distance_per_segment': np.mean(distances),\n", "                'max_distance_segment': np.max(distances),\n", "                'distance_std': np.std(distances),\n", "                'straight_line_distance': self._haversine_distance(lats[0], lons[0], lats[-1], lons[-1]),\n", "                'path_efficiency': self._haversine_distance(lats[0], lons[0], lats[-1], lons[-1]) / max(np.sum(distances), 0.001),\n", "            }\n", "        else:\n", "            geo_trajectory = {\n", "                'total_distance': 0, 'avg_distance_per_segment': 0, 'max_distance_segment': 0,\n", "                'distance_std': 0, 'straight_line_distance': 0, 'path_efficiency': 1\n", "            }\n", "\n", "        return {**geo_basic, **geo_area, **geo_trajectory}\n", "\n", "    def _extract_advanced_temporal_features(self, time_diffs, time_span, num_points):\n", "        \"\"\"Extract sophisticated temporal features\"\"\"\n", "        temporal_basic = {\n", "            'time_span_hours': time_span,\n", "            'num_positions': num_points,\n", "            'positions_per_hour': num_points / max(time_span, 0.1),\n", "        }\n", "\n", "        if len(time_diffs) > 0:\n", "            temporal_advanced = {\n", "                'avg_time_between_positions': np.mean(time_diffs),\n", "                'time_between_positions_std': np.std(time_diffs),\n", "                'max_time_gap': np.max(time_diffs),\n", "                'min_time_gap': np.min(time_diffs),\n", "                'time_gap_cv': np.std(time_diffs) / max(np.mean(time_diffs), 0.001),\n", "                'time_consistency': 1 - (np.std(time_diffs) / max(np.mean(time_diffs), 0.001)),\n", "                'long_gaps_ratio': np.mean(time_diffs > np.median(time_diffs) * 2),\n", "            }\n", "        else:\n", "            temporal_advanced = {\n", "                'avg_time_between_positions': 0, 'time_between_positions_std': 0, 'max_time_gap': 0,\n", "                'min_time_gap': 0, 'time_gap_cv': 0, 'time_consistency': 1, 'long_gaps_ratio': 0\n", "            }\n", "\n", "        return {**temporal_basic, **temporal_advanced}\n", "\n", "    def _extract_behavior_signatures(self, speeds, courses, lats, lons, time_diffs):\n", "        \"\"\"Extract vessel-specific behavior signatures\"\"\"\n", "        # Fishing behavior signatures\n", "        fishing_sig = {\n", "            'fishing_speed_sig': np.mean(speeds < 3) * (1 + np.std(speeds)),\n", "            'fishing_pattern_sig': self._detect_fishing_pattern(speeds, courses),\n", "            'fishing_area_revisit': self._area_revisit_pattern(lats, lons),\n", "        }\n", "\n", "        # Cargo behavior signatures\n", "        cargo_sig = {\n", "            'cargo_speed_sig': np.mean((speeds > 8) & (speeds < 25)) * (1 - np.std(speeds)/max(np.mean(speeds), 1)),\n", "            'cargo_linearity_sig': self._linearity_signature(lats, lons),\n", "            'cargo_efficiency_sig': self._movement_efficiency_signature(speeds, time_diffs),\n", "        }\n", "\n", "        # Pleasure craft signatures\n", "        pleasure_sig = {\n", "            'pleasure_speed_sig': np.mean((speeds > 10) & (speeds < 30)),\n", "            'pleasure_variability_sig': np.std(speeds) / max(np.mean(speeds), 1),\n", "            'pleasure_weekend_pattern': 0,  # Would need day-of-week analysis\n", "        }\n", "\n", "        # Tug boat signatures\n", "        tug_sig = {\n", "            'tug_maneuver_sig': self._maneuverability_signature(courses),\n", "            'tug_speed_sig': np.mean(speeds < 12) * (1 + len([i for i in range(1, len(speeds)) if abs(speeds[i] - speeds[i-1]) > 2]) / max(len(speeds), 1)),\n", "            'tug_work_pattern_sig': self._work_pattern_signature(speeds, courses),\n", "        }\n", "\n", "        return {**fishing_sig, **cargo_sig, **pleasure_sig, **tug_sig}\n", "\n", "    def _extract_operational_patterns(self, speeds, courses, time_diffs):\n", "        \"\"\"Extract operational pattern features\"\"\"\n", "        if len(speeds) == 0:\n", "            return {}\n", "\n", "        # Operating states analysis\n", "        stopped_threshold = 0.5\n", "        slow_threshold = 3\n", "        working_threshold = 5\n", "\n", "        operational = {\n", "            'time_stopped_ratio': np.mean(speeds < stopped_threshold),\n", "            'time_slow_ratio': np.mean((speeds >= stopped_threshold) & (speeds < slow_threshold)),\n", "            'time_working_ratio': np.mean((speeds >= working_threshold)),\n", "            'operational_efficiency': np.mean(speeds > 1) / max(len(speeds), 1),\n", "        }\n", "\n", "        # State transition analysis\n", "        if len(speeds) > 1:\n", "            state_changes = len([i for i in range(1, len(speeds))\n", "                               if (speeds[i] > working_threshold) != (speeds[i-1] > working_threshold)])\n", "            operational['state_transitions'] = state_changes / max(len(speeds), 1)\n", "        else:\n", "            operational['state_transitions'] = 0\n", "\n", "        return operational\n", "\n", "    def _extract_complexity_features(self, speeds, courses, lats, lons):\n", "        \"\"\"Extract statistical complexity features\"\"\"\n", "        complexity = {}\n", "\n", "        # Fractal dimension approximation\n", "        complexity['trajectory_complexity'] = self._estimate_fractal_dimension(lats, lons)\n", "\n", "        # Entropy measures\n", "        if len(speeds) > 0:\n", "            complexity['speed_entropy'] = self._calculate_entropy(speeds, bins=20)\n", "        if len(courses) > 1:\n", "            complexity['course_entropy'] = self._calculate_entropy(courses, bins=36)\n", "\n", "        # Autocorrelation\n", "        if len(speeds) > 10:\n", "            complexity['speed_autocorr'] = self._autocorrelation(speeds, lag=1)\n", "        if len(courses) > 10:\n", "            complexity['course_autocorr'] = self._autocorrelation(courses, lag=1)\n", "\n", "        return complexity\n", "\n", "    def _extract_physical_features(self, vessel_data):\n", "        \"\"\"Extract physical vessel characteristics\"\"\"\n", "        physical = {}\n", "\n", "        if 'Length' in vessel_data.columns and pd.notna(vessel_data['Length'].iloc[0]):\n", "            length = vessel_data['Length'].iloc[0]\n", "            physical['length'] = length\n", "            physical['size_class'] = 1 if length < 50 else (2 if length < 150 else 3)\n", "\n", "            if 'Width' in vessel_data.columns and pd.notna(vessel_data['Width'].iloc[0]):\n", "                width = vessel_data['Width'].iloc[0]\n", "                physical['width'] = width\n", "                physical['length_width_ratio'] = length / max(width, 1)\n", "                physical['vessel_size_index'] = length * width\n", "\n", "        return physical\n", "\n", "    # === HELPER METHODS FOR ADVANCED CALCULATIONS ===\n", "\n", "    def _safe_skewness(self, data):\n", "        \"\"\"Safe skewness calculation\"\"\"\n", "        if len(data) < 3:\n", "            return 0\n", "        mean = np.mean(data)\n", "        std = np.std(data)\n", "        if std == 0:\n", "            return 0\n", "        return np.mean(((data - mean) / std) ** 3)\n", "\n", "    def _safe_kurtosis(self, data):\n", "        \"\"\"Safe kurtosis calculation\"\"\"\n", "        if len(data) < 4:\n", "            return 0\n", "        mean = np.mean(data)\n", "        std = np.std(data)\n", "        if std == 0:\n", "            return 0\n", "        return np.mean(((data - mean) / std) ** 4) - 3\n", "\n", "    def _gini_coefficient(self, data):\n", "        \"\"\"Calculate Gini coefficient\"\"\"\n", "        if len(data) == 0:\n", "            return 0\n", "        sorted_data = np.sort(data)\n", "        n = len(data)\n", "        cumsum = np.cumsum(sorted_data)\n", "        return (2 * np.sum((np.arange(1, n+1) * sorted_data))) / (n * cumsum[-1]) - (n + 1) / n\n", "\n", "    def _detect_zigzag_pattern(self, courses):\n", "        \"\"\"Detect zigzag navigation pattern\"\"\"\n", "        if len(courses) < 4:\n", "            return 0\n", "\n", "        direction_changes = 0\n", "        for i in range(2, len(courses)):\n", "            diff1 = courses[i-1] - courses[i-2]\n", "            diff2 = courses[i] - courses[i-1]\n", "\n", "            # Normalize differences\n", "            diff1 = diff1 % 360\n", "            diff2 = diff2 % 360\n", "            if diff1 > 180:\n", "                diff1 -= 360\n", "            if diff2 > 180:\n", "                diff2 -= 360\n", "\n", "            if diff1 * diff2 < 0 and abs(diff1) > 10 and abs(diff2) > 10:\n", "                direction_changes += 1\n", "\n", "        return direction_changes / max(len(courses) - 2, 1)\n", "\n", "    def _circular_variance(self, angles):\n", "        \"\"\"Calculate circular variance for course data\"\"\"\n", "        if len(angles) == 0:\n", "            return 0\n", "\n", "        # Convert to radians\n", "        angles_rad = np.radians(angles)\n", "\n", "        # Calculate mean direction\n", "        mean_cos = np.mean(np.cos(angles_rad))\n", "        mean_sin = np.mean(np.sin(angles_rad))\n", "\n", "        # Calculate circular variance\n", "        r = np.sqrt(mean_cos**2 + mean_sin**2)\n", "        return 1 - r\n", "\n", "    def _estimate_turning_radius(self, course_changes):\n", "        \"\"\"Estimate typical turning radius from course changes\"\"\"\n", "        if len(course_changes) == 0:\n", "            return 0\n", "\n", "        significant_turns = course_changes[course_changes > 5]\n", "        if len(significant_turns) == 0:\n", "            return 180  # No turns, very large radius\n", "\n", "        return 180 / max(np.mean(significant_turns), 1)\n", "\n", "    def _calculate_entropy(self, data, bins=10):\n", "        \"\"\"Calculate Shannon entropy\"\"\"\n", "        if len(data) == 0:\n", "            return 0\n", "\n", "        hist, _ = np.histogram(data, bins=bins)\n", "        hist = hist[hist > 0]\n", "        probs = hist / np.sum(hist)\n", "        return -np.sum(probs * np.log2(probs))\n", "\n", "    def _convex_hull_ratio(self, lats, lons):\n", "        \"\"\"Calculate ratio of convex hull area to bounding box area\"\"\"\n", "        # Simplified convex hull approximation\n", "        lat_range = np.max(lats) - np.min(lats)\n", "        lon_range = np.max(lons) - np.min(lons)\n", "        bounding_area = lat_range * lon_range\n", "\n", "        if bounding_area == 0:\n", "            return 1\n", "\n", "        # Approximate convex hull area using points on boundary\n", "        unique_points = len(set(zip(np.round(lats, 4), np.round(lons, 4))))\n", "        approx_hull_ratio = min(1.0, unique_points / max(len(lats), 1))\n", "\n", "        return approx_hull_ratio\n", "\n", "    def _calculate_distances(self, lats, lons):\n", "        \"\"\"Calculate distances between consecutive points\"\"\"\n", "        distances = []\n", "        for i in range(1, len(lats)):\n", "            dist = self._haversine_distance(lats[i-1], lons[i-1], lats[i], lons[i])\n", "            distances.append(dist)\n", "        return np.array(distances)\n", "\n", "    def _haversine_distance(self, lat1, lon1, lat2, lon2):\n", "        \"\"\"Calculate Haversine distance between two points\"\"\"\n", "        R = 6371  # Earth's radius in km\n", "\n", "        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])\n", "        dlat = lat2 - lat1\n", "        dlon = lon2 - lon1\n", "\n", "        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2\n", "        c = 2 * np.arcsin(np.sqrt(a))\n", "\n", "        return R * c\n", "\n", "    def _detect_fishing_pattern(self, speeds, courses):\n", "        \"\"\"Detect fishing-specific movement pattern\"\"\"\n", "        if len(speeds) < 5 or len(courses) < 5:\n", "            return 0\n", "\n", "        # Fishing pattern: slow speeds with irregular course changes\n", "        slow_periods = speeds < 3\n", "        if len(courses) > 1:\n", "            course_changes = np.abs(np.diff(courses))\n", "            course_changes = np.minimum(course_changes, 360 - course_changes)\n", "            irregular_movement = course_changes > 20\n", "\n", "            # Align arrays properly\n", "            if len(irregular_movement) == len(slow_periods) - 1:\n", "                slow_periods = slow_periods[1:]  # Remove first element\n", "            elif len(slow_periods) == len(irregular_movement) - 1:\n", "                irregular_movement = irregular_movement[1:]  # Remove first element\n", "\n", "            if len(slow_periods) == len(irregular_movement):\n", "                fishing_pattern = np.mean(slow_periods & irregular_movement)\n", "            else:\n", "                fishing_pattern = np.mean(slow_periods) * 0.5  # Fallback\n", "        else:\n", "            fishing_pattern = np.mean(slow_periods) * 0.5\n", "\n", "        return fishing_pattern\n", "\n", "    def _area_revisit_pattern(self, lats, lons):\n", "        \"\"\"Calculate how often vessel revisits same areas\"\"\"\n", "        if len(lats) < 10:\n", "            return 0\n", "\n", "        # Grid the area into cells and count revisits\n", "        lat_bins = np.linspace(np.min(lats), np.max(lats), 10)\n", "        lon_bins = np.linspace(np.min(lons), np.max(lons), 10)\n", "\n", "        grid_visits = {}\n", "        for lat, lon in zip(lats, lons):\n", "            lat_idx = np.digitize(lat, lat_bins)\n", "            lon_idx = np.digitize(lon, lon_bins)\n", "            grid_key = (lat_idx, lon_idx)\n", "            grid_visits[grid_key] = grid_visits.get(grid_key, 0) + 1\n", "\n", "        # Calculate revisit ratio\n", "        total_visits = sum(grid_visits.values())\n", "        unique_cells = len(grid_visits)\n", "\n", "        return 1 - (unique_cells / max(total_visits, 1))\n", "\n", "    def _linearity_signature(self, lats, lons):\n", "        \"\"\"Calculate how linear the trajectory is\"\"\"\n", "        if len(lats) < 3:\n", "            return 1\n", "\n", "        # Calculate straight-line distance vs actual path distance\n", "        straight_line = self._haversine_distance(lats[0], lons[0], lats[-1], lons[-1])\n", "        actual_path = np.sum(self._calculate_distances(lats, lons))\n", "\n", "        if actual_path == 0:\n", "            return 1\n", "\n", "        return straight_line / actual_path\n", "\n", "    def _movement_efficiency_signature(self, speeds, time_diffs):\n", "        \"\"\"Calculate movement efficiency signature\"\"\"\n", "        if len(speeds) == 0 or len(time_diffs) == 0:\n", "            return 0\n", "\n", "        # Efficiency: consistent speed with minimal stopped time\n", "        speed_consistency = 1 - (np.std(speeds) / max(np.mean(speeds), 0.1))\n", "        active_time_ratio = np.mean(speeds > 1)\n", "\n", "        return speed_consistency * active_time_ratio\n", "\n", "    def _maneuverability_signature(self, courses):\n", "        \"\"\"Calculate maneuverability signature for tugs\"\"\"\n", "        if len(courses) < 3:\n", "            return 0\n", "\n", "        course_changes = np.abs(np.diff(courses))\n", "        course_changes = np.minimum(course_changes, 360 - course_changes)\n", "\n", "        # Tugs have moderate but frequent course changes\n", "        moderate_turns = np.mean((course_changes > 10) & (course_changes < 90))\n", "        frequent_changes = len(course_changes[course_changes > 5]) / max(len(course_changes), 1)\n", "\n", "        return moderate_turns * frequent_changes\n", "\n", "    def _work_pattern_signature(self, speeds, courses):\n", "        \"\"\"Detect work pattern signature\"\"\"\n", "        if len(speeds) < 5:\n", "            return 0\n", "\n", "        # Work pattern: alternating periods of movement and stopping\n", "        speed_changes = np.abs(np.diff(speeds))\n", "        work_cycles = len([i for i in range(1, len(speed_changes))\n", "                          if speed_changes[i] > 1 and speed_changes[i-1] < 1])\n", "\n", "        return work_cycles / max(len(speeds), 1)\n", "\n", "    def _estimate_fractal_dimension(self, lats, lons):\n", "        \"\"\"Estimate fractal dimension of trajectory\"\"\"\n", "        if len(lats) < 4:\n", "            return 1\n", "\n", "        # Box-counting method approximation\n", "        scales = [0.001, 0.01, 0.1]  # Different box sizes\n", "        counts = []\n", "\n", "        for scale in scales:\n", "            # Count boxes containing trajectory points\n", "            lat_bins = np.arange(np.min(lats), np.max(lats) + scale, scale)\n", "            lon_bins = np.arange(np.min(lons), np.max(lons) + scale, scale)\n", "\n", "            occupied_boxes = set()\n", "            for lat, lon in zip(lats, lons):\n", "                lat_idx = np.digitize(lat, lat_bins)\n", "                lon_idx = np.digitize(lon, lon_bins)\n", "                occupied_boxes.add((lat_idx, lon_idx))\n", "\n", "            counts.append(len(occupied_boxes))\n", "\n", "        # Estimate fractal dimension from log-log slope\n", "        if len(counts) > 1 and counts[0] != counts[-1]:\n", "            log_scales = np.log(scales)\n", "            log_counts = np.log(counts)\n", "            slope = (log_counts[-1] - log_counts[0]) / (log_scales[-1] - log_scales[0])\n", "            return max(1, min(2, -slope))  # Constrain between 1 and 2\n", "\n", "        return 1.5  # <PERSON><PERSON><PERSON> moderate complexity\n", "\n", "    def _autocorrelation(self, data, lag=1):\n", "        \"\"\"Calculate autocorrelation at given lag\"\"\"\n", "        if len(data) <= lag:\n", "            return 0\n", "\n", "        n = len(data)\n", "        data = data - np.mean(data)\n", "        autocorr = np.correlate(data[:-lag], data[lag:]) / (np.var(data) * (n - lag))\n", "        return autocorr[0] if len(autocorr) > 0 else 0\n", "\n", "class ExtremePerformanceEnsemble:\n", "    \"\"\"Extreme performance ensemble for >95% accuracy\"\"\"\n", "\n", "    def __init__(self, config: Dict[str, Any]):\n", "        self.config = config\n", "        self.models = {}\n", "        self.meta_model = None\n", "        self.scaler = StandardScaler()\n", "        self.label_encoder = LabelEncoder()\n", "        self.feature_selector = None\n", "        self.is_fitted = False\n", "\n", "    def train(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:\n", "        \"\"\"Train extreme performance ensemble\"\"\"\n", "        print(\"🧠 Training EXTREME PERFORMANCE ensemble...\")\n", "        start_time = time.time()\n", "\n", "        # Encode labels\n", "        y_encoded = self.label_encoder.fit_transform(y)\n", "        n_classes = len(self.label_encoder.classes_)\n", "\n", "        # Feature selection - keep only the most informative features\n", "        print(\"🔍 Performing feature selection...\")\n", "        selector = SelectKBest(score_func=f_classif, k=min(50, X.shape[1]))  # Top 50 features\n", "        X_selected = selector.fit_transform(X, y_encoded)\n", "        self.feature_selector = selector\n", "\n", "        print(f\"📊 Selected {X_selected.shape[1]} most informative features\")\n", "\n", "        # Scale features\n", "        X_scaled = self.scaler.fit_transform(X_selected)\n", "\n", "        # Advanced data splitting with stratification\n", "        X_train, X_test, y_train, y_test = train_test_split(\n", "            X_scaled, y_encoded,\n", "            test_size=0.15,  # Smaller test set for more training data\n", "            random_state=42,\n", "            stratify=y_encoded\n", "        )\n", "\n", "        # Advanced class balancing with SMOTETomek\n", "        print(\"⚖️  Applying advanced class balancing...\")\n", "        smote_tomek = SMOTETomek(random_state=42)\n", "        X_train_balanced, y_train_balanced = smote_tomek.fit_resample(X_train, y_train)\n", "\n", "        print(f\"📈 Training data: {len(X_train_balanced)} samples after balancing\")\n", "\n", "        # Train multiple diverse models with optimized hyperparameters\n", "        self._train_base_models(X_train_balanced, y_train_balanced, n_classes)\n", "\n", "        # Generate meta-features using cross-validation\n", "        print(\"🎯 Generating meta-features...\")\n", "        meta_features = self._generate_meta_features(X_train_balanced, y_train_balanced)\n", "\n", "        # Train meta-learner (stacking)\n", "        print(\"🧠 Training meta-learner...\")\n", "        self.meta_model = LogisticRegression(\n", "            max_iter=2000,\n", "            class_weight='balanced',\n", "            random_state=42,\n", "            solver='lbfgs'\n", "        )\n", "        self.meta_model.fit(meta_features, y_train_balanced)\n", "\n", "        # Final evaluation\n", "        test_meta_features = self._predict_meta_features(X_test)\n", "        final_predictions = self.meta_model.predict(test_meta_features)\n", "        final_accuracy = accuracy_score(y_test, final_predictions)\n", "\n", "        # Individual model evaluations\n", "        individual_accs = {}\n", "        for name, model in self.models.items():\n", "            pred = model.predict(X_test)\n", "            individual_accs[name] = accuracy_score(y_test, pred)\n", "\n", "        training_time = time.time() - start_time\n", "        self.is_fitted = True\n", "\n", "        results = {\n", "            'individual_accuracies': individual_accs,\n", "            'meta_accuracy': final_accuracy,\n", "            'training_time': training_time,\n", "            'n_features_selected': X_selected.shape[1],\n", "            'n_features_original': <PERSON>.shape[1],\n", "            'n_samples': len(X),\n", "            'n_classes': n_classes,\n", "            'class_names': self.label_encoder.classes_,\n", "            'test_predictions': final_predictions,\n", "            'test_actual': y_test,\n", "        }\n", "\n", "        print(f\"✅ EXTREME training completed in {training_time:.1f}s\")\n", "        print(f\"🏆 FINAL META-ENSEMBLE ACCURACY: {final_accuracy:.4f} ({100*final_accuracy:.2f}%)\")\n", "\n", "        for name, acc in individual_accs.items():\n", "            print(f\"   {name}: {acc:.4f}\")\n", "\n", "        return results\n", "\n", "    def _train_base_models(self, X_train, y_train, n_classes):\n", "        \"\"\"Train diverse base models with optimized hyperparameters\"\"\"\n", "\n", "        # Random Forest with aggressive parameters\n", "        print(\"🌲 Training optimized Random Forest...\")\n", "        self.models['rf'] = RandomForestClassifier(\n", "            n_estimators=2000,\n", "            max_depth=30,\n", "            min_samples_split=2,\n", "            min_samples_leaf=1,\n", "            max_features='sqrt',\n", "            class_weight='balanced_subsample',\n", "            random_state=42,\n", "            n_jobs=-1,\n", "            criterion='gini'\n", "        )\n", "        self.models['rf'].fit(X_train, y_train)\n", "\n", "        # Extra Trees for diversity\n", "        print(\"🌳 Training Extra Trees...\")\n", "        self.models['et'] = ExtraTreesClassifier(\n", "            n_estimators=1500,\n", "            max_depth=25,\n", "            min_samples_split=3,\n", "            min_samples_leaf=1,\n", "            max_features='sqrt',\n", "            class_weight='balanced_subsample',\n", "            random_state=43,\n", "            n_jobs=-1\n", "        )\n", "        self.models['et'].fit(X_train, y_train)\n", "\n", "        # LightGBM with extreme parameters\n", "        print(\"⚡ Training extreme LightGBM...\")\n", "        self.models['lgb'] = LGBMClassifier(\n", "            objective='multiclass',\n", "            num_class=n_classes,\n", "            n_estimators=3000,\n", "            max_depth=20,\n", "            learning_rate=0.02,\n", "            feature_fraction=0.8,\n", "            bagging_fraction=0.8,\n", "            bagging_freq=5,\n", "            min_child_samples=10,\n", "            reg_alpha=0.1,\n", "            reg_lambda=0.1,\n", "            class_weight='balanced',\n", "            random_state=42,\n", "            verbosity=-1,\n", "            n_jobs=-1,\n", "            force_col_wise=True\n", "        )\n", "        self.models['lgb'].fit(X_train, y_train)\n", "\n", "        # XGBoost with optimization\n", "        print(\"🚀 Training extreme XGBoost...\")\n", "        self.models['xgb'] = XGBClassifier(\n", "            objective='multi:softprob',\n", "            n_estimators=2000,\n", "            max_depth=12,\n", "            learning_rate=0.03,\n", "            subsample=0.8,\n", "            colsample_bytree=0.8,\n", "            reg_alpha=0.1,\n", "            reg_lambda=0.1,\n", "            random_state=42,\n", "            n_jobs=-1,\n", "            verbosity=0\n", "        )\n", "        self.models['xgb'].fit(X_train, y_train)\n", "\n", "        # CatBoost for additional diversity\n", "        print(\"🐱 Training CatBoost...\")\n", "        self.models['cat'] = CatBoostClassifier(\n", "            iterations=1500,\n", "            depth=10,\n", "            learning_rate=0.05,\n", "            class_weights=[1]*n_classes,\n", "            random_seed=42,\n", "            verbose=False\n", "        )\n", "        self.models['cat'].fit(X_train, y_train)\n", "\n", "        # Gradient Boosting\n", "        print(\"📈 Training Gradient Boosting...\")\n", "        self.models['gb'] = GradientBoostingClassifier(\n", "            n_estimators=1000,\n", "            max_depth=8,\n", "            learning_rate=0.1,\n", "            subsample=0.8,\n", "            random_state=42\n", "        )\n", "        self.models['gb'].fit(X_train, y_train)\n", "\n", "    def _generate_meta_features(self, X_train, y_train):\n", "        \"\"\"Generate meta-features using cross-validation\"\"\"\n", "        kfold = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "        meta_features = np.zeros((len(X_train), len(self.models) * len(self.label_encoder.classes_)))\n", "\n", "        for fold, (train_idx, val_idx) in enumerate(kfold.split(X_train, y_train)):\n", "            X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]\n", "            y_fold_train = y_train[train_idx]\n", "\n", "            feature_idx = 0\n", "            for name, model in self.models.items():\n", "                # Clone and train model on fold\n", "                model_clone = type(model)(**model.get_params())\n", "                model_clone.fit(X_fold_train, y_fold_train)\n", "\n", "                # Get probabilities for validation set\n", "                proba = model_clone.predict_proba(X_fold_val)\n", "\n", "                # Store in meta-features\n", "                n_classes = proba.shape[1]\n", "                meta_features[val_idx, feature_idx:feature_idx+n_classes] = proba\n", "                feature_idx += n_classes\n", "\n", "        return meta_features\n", "\n", "    def _predict_meta_features(self, X_test):\n", "        \"\"\"Generate meta-features for test set\"\"\"\n", "        meta_features = np.zeros((len(X_test), len(self.models) * len(self.label_encoder.classes_)))\n", "\n", "        feature_idx = 0\n", "        for name, model in self.models.items():\n", "            proba = model.predict_proba(X_test)\n", "            n_classes = proba.shape[1]\n", "            meta_features[:, feature_idx:feature_idx+n_classes] = proba\n", "            feature_idx += n_classes\n", "\n", "        return meta_features\n", "\n", "    def predict(self, X: pd.DataFrame) -> np.ndarray:\n", "        \"\"\"Make predictions using the extreme ensemble\"\"\"\n", "        if not self.is_fitted:\n", "            raise ValueError(\"Model must be fitted before prediction\")\n", "\n", "        # Apply same preprocessing\n", "        X_selected = self.feature_selector.transform(X)\n", "        X_scaled = self.scaler.transform(X_selected)\n", "\n", "        # Generate meta-features\n", "        meta_features = self._predict_meta_features(X_scaled)\n", "\n", "        # Final prediction\n", "        predictions = self.meta_model.predict(meta_features)\n", "\n", "        return self.label_encoder.inverse_transform(predictions)\n", "\n", "class UltimateVisualizer:\n", "    \"\"\"Ultimate visualization for >95% results\"\"\"\n", "\n", "    @staticmethod\n", "    def plot_ultimate_results(results: Dict[str, Any]):\n", "        \"\"\"Create ultimate results visualization\"\"\"\n", "        fig, axes = plt.subplots(3, 3, figsize=(24, 18))\n", "        fig.suptitle('🏆 EXTREME PERFORMANCE MARITIME CLASSIFICATION - ULTIMATE RESULTS',\n", "                    fontsize=18, fontweight='bold')\n", "\n", "        # Individual model accuracies\n", "        models = list(results['individual_accuracies'].keys())\n", "        accuracies = list(results['individual_accuracies'].values())\n", "        accuracies.append(results['meta_accuracy'])\n", "        models.append('META-ENSEMBLE')\n", "\n", "        colors = plt.cm.viridis(np.linspace(0, 1, len(models)))\n", "        bars = axes[0, 0].bar(models, accuracies, color=colors)\n", "        axes[0, 0].set_title('🎯 Model Performance Comparison', fontweight='bold', fontsize=14)\n", "        axes[0, 0].set_ylabel('Accuracy')\n", "        axes[0, 0].set_ylim(0.8, 1.0)\n", "\n", "        # Add percentage labels\n", "        for bar, acc in zip(bars, accuracies):\n", "            height = bar.get_height()\n", "            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.005,\n", "                           f'{acc:.1%}', ha='center', va='bottom', fontweight='bold')\n", "\n", "        axes[0, 0].tick_params(axis='x', rotation=45)\n", "        axes[0, 0].axhline(y=0.95, color='gold', linestyle='--', linewidth=2, label='95% TARGET')\n", "        axes[0, 0].legend()\n", "\n", "        # Success indicator\n", "        success_color = 'green' if results['meta_accuracy'] >= 0.95 else ('orange' if results['meta_accuracy'] >= 0.90 else 'red')\n", "        success_text = '🏆 TARGET ACHIEVED!' if results['meta_accuracy'] >= 0.95 else ('⚠️ CLOSE' if results['meta_accuracy'] >= 0.90 else '❌ MISSED')\n", "\n", "        axes[0, 1].text(0.5, 0.7, f\"{success_text}\", fontsize=20, fontweight='bold',\n", "                       ha='center', va='center', transform=axes[0, 1].transAxes, color=success_color)\n", "        axes[0, 1].text(0.5, 0.5, f\"FINAL ACCURACY\", fontsize=16, fontweight='bold',\n", "                       ha='center', va='center', transform=axes[0, 1].transAxes)\n", "        axes[0, 1].text(0.5, 0.3, f\"{results['meta_accuracy']:.2%}\", fontsize=24, fontweight='bold',\n", "                       ha='center', va='center', transform=axes[0, 1].transAxes, color=success_color)\n", "        axes[0, 1].axis('off')\n", "\n", "        # Feature information\n", "        axes[0, 2].text(0.1, 0.9, f\"📊 FEATURE ANALYSIS\", fontsize=14, fontweight='bold', transform=axes[0, 2].transAxes)\n", "        axes[0, 2].text(0.1, 0.8, f\"Original Features: {results['n_features_original']}\", fontsize=12, transform=axes[0, 2].transAxes)\n", "        axes[0, 2].text(0.1, 0.7, f\"Selected Features: {results['n_features_selected']}\", fontsize=12, transform=axes[0, 2].transAxes)\n", "        axes[0, 2].text(0.1, 0.6, f\"Feature Reduction: {100*(1-results['n_features_selected']/results['n_features_original']):.1f}%\",\n", "                       fontsize=12, transform=axes[0, 2].transAxes)\n", "        axes[0, 2].text(0.1, 0.5, f\"Training Samples: {results['n_samples']:,}\", fontsize=12, transform=axes[0, 2].transAxes)\n", "        axes[0, 2].text(0.1, 0.4, f\"Classes: {results['n_classes']}\", fontsize=12, transform=axes[0, 2].transAxes)\n", "        axes[0, 2].text(0.1, 0.3, f\"Training Time: {results['training_time']:.1f}s\", fontsize=12, transform=axes[0, 2].transAxes)\n", "        axes[0, 2].axis('off')\n", "\n", "        # Confusion Matrix\n", "        cm = confusion_matrix(results['test_actual'], results['test_predictions'])\n", "        class_names = results['class_names']\n", "\n", "        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "                   xticklabels=class_names, yticklabels=class_names, ax=axes[1, 0])\n", "        axes[1, 0].set_title('🎯 Confusion Matrix - Meta-Ensemble', fontweight='bold')\n", "        axes[1, 0].set_xlabel('Predicted')\n", "        axes[1, 0].set_ylabel('Actual')\n", "\n", "        # Per-class metrics\n", "        report = classification_report(results['test_actual'], results['test_predictions'],\n", "                                     target_names=class_names, output_dict=True)\n", "\n", "        metrics = ['precision', 'recall', 'f1-score']\n", "        class_metrics = {cls: [report[cls][metric] for metric in metrics] for cls in class_names}\n", "\n", "        x = np.arange(len(class_names))\n", "        width = 0.25\n", "\n", "        for i, metric in enumerate(metrics):\n", "            values = [class_metrics[cls][i] for cls in class_names]\n", "            axes[1, 1].bar(x + i*width, values, width, label=metric.title(), alpha=0.8)\n", "\n", "        axes[1, 1].set_title('📈 Per-Class Performance', fontweight='bold')\n", "        axes[1, 1].set_xlabel('Vessel Type')\n", "        axes[1, 1].set_ylabel('Score')\n", "        axes[1, 1].set_xticks(x + width)\n", "        axes[1, 1].set_xticklabels(class_names, rotation=45)\n", "        axes[1, 1].legend()\n", "        axes[1, 1].set_ylim(0, 1.1)\n", "\n", "        # Model accuracy distribution\n", "        all_accs = list(results['individual_accuracies'].values()) + [results['meta_accuracy']]\n", "        axes[1, 2].hist(all_accs, bins=10, alpha=0.7, color='skyblue', edgecolor='black')\n", "        axes[1, 2].axvline(results['meta_accuracy'], color='red', linestyle='--', linewidth=2, label='Meta-Ensemble')\n", "        axes[1, 2].axvline(0.95, color='gold', linestyle='--', linewidth=2, label='Target (95%)')\n", "        axes[1, 2].set_title('📊 Accuracy Distribution', fontweight='bold')\n", "        axes[1, 2].set_xlabel('Accuracy')\n", "        axes[1, 2].set_ylabel('Count')\n", "        axes[1, 2].legend()\n", "\n", "        # Performance timeline\n", "        model_names = list(results['individual_accuracies'].keys()) + ['Meta']\n", "        model_accs = list(results['individual_accuracies'].values()) + [results['meta_accuracy']]\n", "\n", "        axes[2, 0].plot(model_names, model_accs, 'o-', linewidth=2, markersize=8)\n", "        axes[2, 0].axhline(y=0.95, color='gold', linestyle='--', label='95% Target')\n", "        axes[2, 0].set_title('🚀 Model Evolution', fontweight='bold')\n", "        axes[2, 0].set_ylabel('Accuracy')\n", "        axes[2, 0].tick_params(axis='x', rotation=45)\n", "        axes[2, 0].legend()\n", "        axes[2, 0].grid(True, alpha=0.3)\n", "\n", "        # System performance\n", "        memory_usage = ResourceManager.get_memory_usage()\n", "        axes[2, 1].text(0.1, 0.9, f\"💻 SYSTEM PERFORMANCE\", fontsize=14, fontweight='bold', transform=axes[2, 1].transAxes)\n", "        axes[2, 1].text(0.1, 0.8, f\"Memory Usage: {memory_usage:.1f}MB\", fontsize=12, transform=axes[2, 1].transAxes)\n", "        axes[2, 1].text(0.1, 0.7, f\"Models Trained: {len(results['individual_accuracies']) + 1}\", fontsize=12, transform=axes[2, 1].transAxes)\n", "\n", "        performance_grade = 'A+' if results['meta_accuracy'] >= 0.95 else ('A' if results['meta_accuracy'] >= 0.90 else 'B')\n", "        axes[2, 1].text(0.1, 0.6, f\"Performance Grade: {performance_grade}\", fontsize=12, fontweight='bold',\n", "                       color=success_color, transform=axes[2, 1].transAxes)\n", "        axes[2, 1].text(0.1, 0.5, f\"Status: Production Ready\", fontsize=12, color='green', transform=axes[2, 1].transAxes)\n", "        axes[2, 1].axis('off')\n", "\n", "        # Achievement summary\n", "        achievements = []\n", "        if results['meta_accuracy'] >= 0.95:\n", "            achievements.append(\"🏆 >95% Accuracy Achieved!\")\n", "        if results['meta_accuracy'] >= 0.90:\n", "            achievements.append(\"✅ >90% Accuracy Achieved!\")\n", "        if results['training_time'] < 300:\n", "            achievements.append(\"⚡ Fast Training (<5min)\")\n", "        if results['n_features_selected'] < results['n_features_original']:\n", "            achievements.append(\"🔍 Smart Feature Selection\")\n", "\n", "        axes[2, 2].text(0.1, 0.9, f\"🏅 ACHIEVEMENTS\", fontsize=14, fontweight='bold', transform=axes[2, 2].transAxes)\n", "        for i, achievement in enumerate(achievements):\n", "            axes[2, 2].text(0.1, 0.8 - i*0.1, achievement, fontsize=11, transform=axes[2, 2].transAxes)\n", "        axes[2, 2].axis('off')\n", "\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# Main execution pipeline\n", "def run_extreme_performance_pipeline():\n", "    \"\"\"Execute the extreme performance maritime vessel classification pipeline\"\"\"\n", "\n", "    config = {\n", "        'min_trajectory_length': 6,  # Slightly reduced for more data\n", "        'test_size': 0.15,\n", "        'random_state': 42\n", "    }\n", "\n", "    print(\"🔍 Checking system resources...\")\n", "    ResourceManager.check_resources()\n", "\n", "    # Mount Google Drive\n", "    try:\n", "        from google.colab import drive\n", "        drive.mount('/content/drive')\n", "        data_path = '/content/drive/MyDrive/DATA/AIS_2024_10_24.zip'\n", "        print(\"☁️  Google Drive mounted successfully\")\n", "    except:\n", "        data_path = 'data/AIS_2024_10_24.zip'\n", "        print(\"💻 Running in local environment\")\n", "\n", "    if not os.path.exists(data_path):\n", "        print(f\"❌ Data file not found: {data_path}\")\n", "        return None\n", "\n", "    # Initialize extreme components\n", "    processor = AdvancedDataProcessor(config)\n", "    ensemble = ExtremePerformanceEnsemble(config)\n", "\n", "    try:\n", "        # Advanced data processing\n", "        df = processor.load_and_validate_data(data_path)\n", "        df_clean = processor.advanced_data_cleaning(df)\n", "\n", "        # World-class feature extraction\n", "        X, y = processor.extract_world_class_features(df_clean)\n", "\n", "        print(f\"\\n📈 EXTREME Dataset Summary:\")\n", "        print(f\"   Features: {X.shape[1]} (world-class feature engineering)\")\n", "        print(f\"   Samples: {X.shape[0]} (high-quality filtered)\")\n", "        print(f\"   Classes: {y.nunique()}\")\n", "        print(f\"   Class Distribution:\")\n", "        for cls, count in y.value_counts().items():\n", "            print(f\"     {cls}: {count} ({100*count/len(y):.1f}%)\")\n", "\n", "        # Train extreme ensemble\n", "        results = ensemble.train(X, y)\n", "\n", "        # Ultimate visualization\n", "        UltimateVisualizer.plot_ultimate_results(results)\n", "\n", "        # Final assessment\n", "        ResourceManager.optimize_memory()\n", "\n", "        print(\"\\n\" + \"=\"*80)\n", "        print(\"🏆 EXTREME PERFORMANCE PIPELINE COMPLETED!\")\n", "        print(f\"🎯 FINAL META-ENSEMBLE ACCURACY: {results['meta_accuracy']:.4f} ({100*results['meta_accuracy']:.2f}%)\")\n", "\n", "        if results['meta_accuracy'] >= 0.95:\n", "            print(\"🎊 🎊 🎊 EXCEPTIONAL SUCCESS! >95% ACCURACY ACHIEVED! 🎊 🎊 🎊\")\n", "            print(\"🏆 WORLD-CLASS MARITIME VESSEL CLASSIFICATION!\")\n", "            print(\"🚀 READY FOR PRODUCTION DEPLOYMENT!\")\n", "        elif results['meta_accuracy'] >= 0.90:\n", "            print(\"✅ EXCELLENT PERFORMANCE! >90% Accuracy achieved!\")\n", "        else:\n", "            print(\"📈 Good performance, consider data quality improvements\")\n", "\n", "        print(\"=\"*80)\n", "\n", "        return ensemble, results\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error in extreme pipeline: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return None, None\n", "\n", "# Execute the extreme pipeline\n", "if __name__ == \"__main__\":\n", "    print(\"🚀 Starting EXTREME PERFORMANCE maritime classification...\")\n", "    model, results = run_extreme_performance_pipeline()\n", "\n", "    if model and results:\n", "        print(\"\\n🏆 EXTREME PERFORMANCE MODEL READY!\")\n", "        print(\"💾 Save: joblib.dump(model, 'extreme_maritime_classifier.joblib')\")\n", "\n", "        if results['meta_accuracy'] >= 0.95:\n", "            print(\"\\n🎯🎯🎯 MISSION ACCOMPLISHED! 🎯🎯🎯\")\n", "            print(\"✅ >95% ACCURACY TARGET ACHIEVED!\")\n", "            print(\"🌟 WORLD-CLASS PERFORMANCE DELIVERED!\")\n", "    else:\n", "        print(\"❌ Extreme pipeline failed. Check error messages.\")"]}]}