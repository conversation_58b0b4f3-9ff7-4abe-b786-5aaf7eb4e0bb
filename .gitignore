# Node / Frontend Dependencies
frontend/node_modules/
frontend/build/
frontend/dist/
frontend/.pnp
frontend/.pnp.js
frontend/.yarn/install-state.gz
frontend/coverage/
frontend/.nyc_output/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Python / Backend Dependencies
functions/venv/
functions/__pycache__/
functions/*.pyc
functions/*.pyo
functions/*.pyd
functions/.pytest_cache/
functions/.coverage
functions/htmlcov/
functions/.tox/
functions/.cache
functions/nosetests.xml
functions/coverage.xml
functions/*.cover
functions/.hypothesis/

# Machine Learning Models (large files)
functions/assets/*.joblib
functions/assets/*.pkl
functions/assets/*.model
*.joblib
*.pkl
*.model

# Firebase
.firebase/
firebase-debug.log
*.emulators.log
.firebaserc

# IDE / OS specific
.vscode/
.idea/
.DS_Store
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.old

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Debug files
pglite-debug.log
debug.log