{"functions": [{"source": "functions", "codebase": "default", "runtime": "python311", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.pyc", "__pycache__", ".pytest_cache", "venv"], "predeploy": ["python -m pip install -r functions/requirements.txt"]}], "hosting": {"public": "frontend/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "firestore": {"rules": "firestore.rules"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 8082, "timeout": "60s"}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "pubsub": {"port": 8085}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}